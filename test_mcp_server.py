#!/usr/bin/env python3
"""
Script para probar el servidor MCP de Database Explorer
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any

class MCPTester:
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        
    async def test_endpoint(self, endpoint: str, method: str = "GET", data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Probar un endpoint específico"""
        url = f"{self.base_url}{endpoint}"
        
        async with aiohttp.ClientSession() as session:
            try:
                if method == "GET":
                    async with session.get(url) as response:
                        result = await response.json()
                        return {
                            "status": response.status,
                            "success": response.status == 200,
                            "data": result
                        }
                elif method == "POST":
                    async with session.post(url, json=data) as response:
                        result = await response.json()
                        return {
                            "status": response.status,
                            "success": response.status == 200,
                            "data": result
                        }
            except Exception as e:
                return {
                    "status": 500,
                    "success": False,
                    "error": str(e)
                }
    
    async def test_all_endpoints(self):
        """Probar todos los endpoints MCP disponibles"""
        print("🧪 Probando servidor MCP Database Explorer")
        print("=" * 50)
        
        # Test 1: Listar bases de datos
        print("\n1. 📊 Probando: Listar bases de datos")
        result = await self.test_endpoint("/mcp/list_databases")
        print(f"   Status: {result['status']}")
        print(f"   Resultado: {result.get('data', result.get('error'))}")
        
        # Test 2: Verificar endpoint MCP principal
        print("\n2. 🔗 Probando: Endpoint MCP principal")
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/mcp") as response:
                    print(f"   Status: {response.status}")
                    if response.headers.get('content-type', '').startswith('text/event-stream'):
                        print("   ✅ Endpoint MCP SSE funcionando correctamente")
                    else:
                        content = await response.text()
                        print(f"   Contenido: {content[:200]}...")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test 3: Verificar documentación OpenAPI
        print("\n3. 📚 Probando: Documentación OpenAPI")
        result = await self.test_endpoint("/openapi.json")
        if result['success']:
            openapi_data = result['data']
            print(f"   ✅ OpenAPI disponible - Título: {openapi_data.get('info', {}).get('title')}")
            print(f"   Versión: {openapi_data.get('info', {}).get('version')}")
            
            # Mostrar endpoints MCP disponibles
            paths = openapi_data.get('paths', {})
            mcp_endpoints = [path for path in paths.keys() if path.startswith('/mcp/')]
            print(f"   Endpoints MCP encontrados: {len(mcp_endpoints)}")
            for endpoint in mcp_endpoints:
                print(f"     - {endpoint}")
        else:
            print(f"   ❌ Error obteniendo OpenAPI: {result.get('error')}")
        
        # Test 4: Probar endpoints MCP específicos (sin base de datos configurada)
        print("\n4. 🗄️ Probando endpoints específicos (sin DB configurada)")
        
        endpoints_to_test = [
            ("/mcp/list_schemas?db_name=test", "GET"),
            ("/mcp/list_tables?db_name=test&schema=public", "GET"),
            ("/mcp/describe_table?db_name=test&schema=public&table=users", "GET"),
        ]
        
        for endpoint, method in endpoints_to_test:
            print(f"\n   Probando: {endpoint}")
            result = await self.test_endpoint(endpoint, method)
            print(f"   Status: {result['status']}")
            if result['success']:
                print(f"   ✅ Respuesta: {result['data']}")
            else:
                print(f"   ⚠️  Error esperado (sin DB): {result.get('data', result.get('error'))}")

    async def test_with_mock_db(self):
        """Probar con configuración de base de datos simulada"""
        print("\n" + "=" * 50)
        print("🔧 Configuración para pruebas con base de datos real:")
        print("=" * 50)
        
        print("""
Para probar con una base de datos real, configura las variables de entorno:

export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=postgres
export DB_PASSWORD=tu_password
export DB_NAME=tu_database

Luego reinicia el servidor:
uvicorn app.main:app --reload --host 0.0.0.0 --port 8001
        """)

async def main():
    """Función principal"""
    tester = MCPTester()
    await tester.test_all_endpoints()
    await tester.test_with_mock_db()
    
    print("\n" + "=" * 50)
    print("✅ Pruebas completadas!")
    print("=" * 50)
    print("""
📋 Resumen de formas de probar tu servidor MCP:

1. 🌐 Swagger UI: http://localhost:8001/docs
2. 📖 ReDoc: http://localhost:8001/redoc  
3. 🔗 Endpoint MCP: http://localhost:8001/mcp
4. 🧪 Script de pruebas: python test_mcp_server.py
5. 📱 Cliente MCP: Usar mcp-remote o Claude Desktop
6. 🐳 Docker: docker-compose up (si tienes docker-compose.yml)

Para integrar con Claude Desktop, agrega esta configuración:
{
    "mcpServers": {
        "database-explorer": {
            "command": "npx",
            "args": [
                "-y", 
                "mcp-remote@latest", 
                "http://localhost:8001/mcp",
                "--allow-http"
            ]
        }
    }
}
    """)

if __name__ == "__main__":
    asyncio.run(main())
