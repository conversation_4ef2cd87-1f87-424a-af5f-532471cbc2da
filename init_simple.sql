-- Inicialización simple de base de datos para pruebas MCP

-- <PERSON><PERSON><PERSON> esquemas
CREATE SCHEMA IF NOT EXISTS sales;
CREATE SCHEMA IF NOT EXISTS inventory;

-- Tabla de usuarios
CREATE TABLE IF NOT EXISTS public.users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    first_name VA<PERSON>HA<PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de categorías
CREATE TABLE IF NOT EXISTS public.categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de productos
CREATE TABLE IF NOT EXISTS public.products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    category_id INTEGER REFERENCES public.categories(id),
    stock_quantity INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de clientes
CREATE TABLE IF NOT EXISTS sales.customers (
    id SERIAL PRIMARY KEY,
    company_name VARCHAR(200),
    contact_name VARCHAR(100) NOT NULL,
    contact_email VARCHAR(100),
    phone VARCHAR(20),
    city VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de órdenes
CREATE TABLE IF NOT EXISTS sales.orders (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER REFERENCES sales.customers(id),
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    total_amount DECIMAL(12,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending'
);

-- Insertar datos de ejemplo
INSERT INTO public.users (username, email, first_name, last_name) VALUES
('admin', '<EMAIL>', 'Admin', 'User'),
('jdoe', '<EMAIL>', 'John', 'Doe'),
('msmith', '<EMAIL>', 'Mary', 'Smith')
ON CONFLICT (username) DO NOTHING;

INSERT INTO public.categories (name, description) VALUES
('Electronics', 'Electronic devices and accessories'),
('Office Supplies', 'Office equipment and supplies'),
('Software', 'Software licenses and applications')
ON CONFLICT DO NOTHING;

INSERT INTO public.products (name, description, price, category_id, stock_quantity) VALUES
('Laptop Dell XPS 13', 'High-performance ultrabook', 1299.99, 1, 15),
('Wireless Mouse', 'Ergonomic wireless mouse', 29.99, 1, 50),
('Microsoft Office 365', 'Annual subscription', 99.99, 3, 100)
ON CONFLICT DO NOTHING;

INSERT INTO sales.customers (company_name, contact_name, contact_email, city) VALUES
('Tech Solutions Inc', 'Sarah Connor', '<EMAIL>', 'New York'),
('Global Corp', 'Michael Brown', '<EMAIL>', 'Los Angeles')
ON CONFLICT DO NOTHING;

INSERT INTO sales.orders (customer_id, total_amount, status) VALUES
(1, 1599.97, 'completed'),
(2, 449.98, 'pending')
ON CONFLICT DO NOTHING;

SELECT 'Base de datos inicializada correctamente' as status;
