-- Inicialización de base de datos para pruebas MCP
-- Este script crea esquemas, tablas y datos de ejemplo

-- Crear esquemas
CREATE SCHEMA IF NOT EXISTS sales;
CREATE SCHEMA IF NOT EXISTS inventory;
CREATE SCHEMA IF NOT EXISTS hr;

-- ============================================
-- ESQUEMA PUBLIC - Usuarios y productos básicos
-- ============================================

-- Tabla de usuarios
CREATE TABLE public.users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE public.users IS 'Tabla principal de usuarios del sistema';
COMMENT ON COLUMN public.users.id IS 'Identificador único del usuario';
COMMENT ON COLUMN public.users.username IS 'Nombre de usuario único para login';
COMMENT ON COLUMN public.users.email IS 'Correo electrónico del usuario';

-- Tabla de categorías
CREATE TABLE public.categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de productos
CREATE TABLE public.products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    category_id INTEGER REFERENCES public.categories(id),
    stock_quantity INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE public.products IS 'Catálogo de productos disponibles';
COMMENT ON COLUMN public.products.price IS 'Precio del producto en la moneda base';

-- ============================================
-- ESQUEMA SALES - Ventas y transacciones
-- ============================================

-- Tabla de clientes
CREATE TABLE sales.customers (
    id SERIAL PRIMARY KEY,
    company_name VARCHAR(200),
    contact_name VARCHAR(100) NOT NULL,
    contact_email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de órdenes
CREATE TABLE sales.orders (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER REFERENCES sales.customers(id),
    user_id INTEGER REFERENCES public.users(id),
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    total_amount DECIMAL(12,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    notes TEXT
);

-- Tabla de detalles de orden
CREATE TABLE sales.order_details (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES sales.orders(id),
    product_id INTEGER REFERENCES public.products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    discount DECIMAL(5,2) DEFAULT 0
);

-- ============================================
-- ESQUEMA INVENTORY - Inventario y almacén
-- ============================================

-- Tabla de proveedores
CREATE TABLE inventory.suppliers (
    id SERIAL PRIMARY KEY,
    company_name VARCHAR(200) NOT NULL,
    contact_name VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de movimientos de inventario
CREATE TABLE inventory.stock_movements (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES public.products(id),
    movement_type VARCHAR(20) NOT NULL, -- 'in', 'out', 'adjustment'
    quantity INTEGER NOT NULL,
    reference_id INTEGER, -- ID de orden, compra, etc.
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES public.users(id)
);

-- ============================================
-- ESQUEMA HR - Recursos Humanos
-- ============================================

-- Tabla de empleados
CREATE TABLE hr.employees (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES public.users(id),
    employee_code VARCHAR(20) UNIQUE,
    department VARCHAR(100),
    position VARCHAR(100),
    salary DECIMAL(10,2),
    hire_date DATE,
    manager_id INTEGER REFERENCES hr.employees(id),
    is_active BOOLEAN DEFAULT true
);

-- ============================================
-- INSERTAR DATOS DE EJEMPLO
-- ============================================

-- Usuarios
INSERT INTO public.users (username, email, first_name, last_name) VALUES
('admin', '<EMAIL>', 'Admin', 'User'),
('jdoe', '<EMAIL>', 'John', 'Doe'),
('msmith', '<EMAIL>', 'Mary', 'Smith'),
('bwilson', '<EMAIL>', 'Bob', 'Wilson'),
('ajohnson', '<EMAIL>', 'Alice', 'Johnson');

-- Categorías
INSERT INTO public.categories (name, description) VALUES
('Electronics', 'Electronic devices and accessories'),
('Office Supplies', 'Office equipment and supplies'),
('Software', 'Software licenses and applications'),
('Furniture', 'Office and home furniture');

-- Productos
INSERT INTO public.products (name, description, price, category_id, stock_quantity) VALUES
('Laptop Dell XPS 13', 'High-performance ultrabook', 1299.99, 1, 15),
('Wireless Mouse', 'Ergonomic wireless mouse', 29.99, 1, 50),
('Office Chair', 'Ergonomic office chair with lumbar support', 299.99, 4, 8),
('Microsoft Office 365', 'Annual subscription', 99.99, 3, 100),
('Monitor 24"', '24-inch LED monitor', 199.99, 1, 20),
('Desk Lamp', 'LED desk lamp with adjustable brightness', 49.99, 4, 25);

-- Clientes
INSERT INTO sales.customers (company_name, contact_name, contact_email, phone, city, country) VALUES
('Tech Solutions Inc', 'Sarah Connor', '<EMAIL>', '******-0101', 'New York', 'USA'),
('Global Corp', 'Michael Brown', '<EMAIL>', '******-0102', 'Los Angeles', 'USA'),
('StartUp Ltd', 'Emma Davis', '<EMAIL>', '******-0103', 'San Francisco', 'USA'),
('Enterprise Co', 'David Miller', '<EMAIL>', '******-0104', 'Chicago', 'USA');

-- Órdenes
INSERT INTO sales.orders (customer_id, user_id, total_amount, status) VALUES
(1, 2, 1599.97, 'completed'),
(2, 3, 449.98, 'pending'),
(3, 2, 99.99, 'completed'),
(4, 4, 799.96, 'processing');

-- Detalles de órdenes
INSERT INTO sales.order_details (order_id, product_id, quantity, unit_price) VALUES
(1, 1, 1, 1299.99),
(1, 2, 10, 29.99),
(2, 3, 1, 299.99),
(2, 6, 3, 49.99),
(3, 4, 1, 99.99),
(4, 5, 4, 199.99);

-- Proveedores
INSERT INTO inventory.suppliers (company_name, contact_name, email, phone) VALUES
('Dell Technologies', 'James Wilson', '<EMAIL>', '******-DELL'),
('Microsoft Corp', 'Lisa Anderson', '<EMAIL>', '******-MSFT'),
('Office Depot', 'Tom Garcia', '<EMAIL>', '******-DEPOT');

-- Empleados
INSERT INTO hr.employees (user_id, employee_code, department, position, salary, hire_date) VALUES
(1, 'EMP001', 'IT', 'System Administrator', 75000.00, '2020-01-15'),
(2, 'EMP002', 'Sales', 'Sales Representative', 55000.00, '2021-03-10'),
(3, 'EMP003', 'Sales', 'Sales Manager', 70000.00, '2019-06-01'),
(4, 'EMP004', 'Operations', 'Operations Specialist', 60000.00, '2022-01-20'),
(5, 'EMP005', 'HR', 'HR Coordinator', 50000.00, '2021-09-15');

-- Movimientos de inventario
INSERT INTO inventory.stock_movements (product_id, movement_type, quantity, notes, created_by) VALUES
(1, 'in', 20, 'Initial stock', 1),
(2, 'in', 100, 'Bulk purchase', 1),
(1, 'out', 5, 'Sales', 2),
(2, 'out', 50, 'Sales', 2),
(3, 'in', 10, 'New shipment', 1);

-- Crear índices para mejor rendimiento
CREATE INDEX idx_users_username ON public.users(username);
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_products_category ON public.products(category_id);
CREATE INDEX idx_orders_customer ON sales.orders(customer_id);
CREATE INDEX idx_orders_date ON sales.orders(order_date);
CREATE INDEX idx_stock_movements_product ON inventory.stock_movements(product_id);

-- Crear vistas útiles
CREATE VIEW sales.order_summary AS
SELECT 
    o.id,
    o.order_date,
    c.company_name,
    c.contact_name,
    u.username as sales_rep,
    o.total_amount,
    o.status
FROM sales.orders o
JOIN sales.customers c ON o.customer_id = c.id
JOIN public.users u ON o.user_id = u.id;

CREATE VIEW inventory.current_stock AS
SELECT 
    p.id,
    p.name,
    p.stock_quantity,
    COALESCE(SUM(CASE WHEN sm.movement_type = 'in' THEN sm.quantity ELSE -sm.quantity END), 0) as calculated_stock
FROM public.products p
LEFT JOIN inventory.stock_movements sm ON p.id = sm.product_id
GROUP BY p.id, p.name, p.stock_quantity;

-- Mensaje de confirmación
SELECT 'Base de datos inicializada correctamente con datos de ejemplo' as status;
