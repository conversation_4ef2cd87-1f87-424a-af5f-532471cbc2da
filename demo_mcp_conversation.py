#!/usr/bin/env python3
"""
Demostración completa de una conversación con el servidor MCP
Simula cómo Claude Desktop interactuaría con tu servidor
"""

import requests
import json
import time
from urllib.parse import quote

class MCPDemo:
    def __init__(self, base_url="http://localhost:8001"):
        self.base_url = base_url
        
    def print_conversation(self, role, message, data=None):
        """Imprimir conversación simulada"""
        if role == "claude":
            print(f"\n🤖 Claude: {message}")
        elif role == "server":
            print(f"🔧 Servidor MCP: {message}")
            if data:
                print(f"   📊 Datos: {json.dumps(data, indent=2, ensure_ascii=False)}")
        elif role == "system":
            print(f"\n⚙️  Sistema: {message}")
        
    def make_request(self, endpoint, method="GET", params=None):
        """Hacer petición al servidor"""
        try:
            url = f"{self.base_url}{endpoint}"
            if method == "GET":
                response = requests.get(url, params=params)
            elif method == "POST":
                response = requests.post(url, params=params)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Status {response.status_code}: {response.text}"}
        except Exception as e:
            return {"error": str(e)}
    
    def run_demo(self):
        """Ejecutar demostración completa"""
        print("🎭 DEMOSTRACIÓN: Conversación con servidor MCP Database Explorer")
        print("=" * 70)
        
        self.print_conversation("system", "Iniciando conexión con servidor MCP...")
        
        # 1. Listar bases de datos
        self.print_conversation("claude", "¿Qué bases de datos tienes disponibles?")
        databases = self.make_request("/mcp/list_databases")
        self.print_conversation("server", "Aquí están las bases de datos disponibles:", databases)
        
        if not databases or "error" in databases:
            self.print_conversation("system", "❌ Error: No se pudo conectar al servidor")
            return
        
        if not databases:
            self.print_conversation("claude", "No hay bases de datos configuradas.")
            return
        
        db_name = databases[0]
        
        # 2. Listar esquemas
        time.sleep(1)
        self.print_conversation("claude", f"Perfecto! Muéstrame los esquemas de '{db_name}'")
        schemas = self.make_request("/mcp/list_schemas", params={"db_name": db_name})
        self.print_conversation("server", f"Esquemas en {db_name}:", schemas)
        
        if not schemas or "error" in schemas:
            return
        
        # 3. Explorar esquema public
        schema_name = "public"
        time.sleep(1)
        self.print_conversation("claude", f"¿Qué tablas hay en el esquema '{schema_name}'?")
        tables = self.make_request("/mcp/list_tables", params={"db_name": db_name, "schema": schema_name})
        self.print_conversation("server", f"Tablas en {schema_name}:", tables)
        
        if not tables or "error" in tables:
            return
        
        # 4. Describir tabla users
        table_name = "users"
        time.sleep(1)
        self.print_conversation("claude", f"¿Puedes describir la estructura de la tabla '{table_name}'?")
        table_desc = self.make_request("/mcp/describe_table", 
                                     params={"db_name": db_name, "schema": schema_name, "table": table_name})
        self.print_conversation("server", f"Estructura de {table_name}:", table_desc)
        
        # 5. Ejecutar consulta
        time.sleep(1)
        query = "SELECT * FROM users LIMIT 3"
        self.print_conversation("claude", f"¿Puedes ejecutar esta consulta: {query}?")
        result = self.make_request("/mcp/execute_query", method="POST",
                                 params={"db_name": db_name, "query": query})
        self.print_conversation("server", "Resultado de la consulta:", result)
        
        # 6. Explorar más tablas
        time.sleep(1)
        self.print_conversation("claude", "¿Qué hay en la tabla products?")
        query2 = "SELECT * FROM products"
        result2 = self.make_request("/mcp/execute_query", method="POST",
                                  params={"db_name": db_name, "query": query2})
        self.print_conversation("server", "Productos disponibles:", result2)
        
        # 7. Explorar otro esquema
        time.sleep(1)
        schema_name2 = "sales"
        self.print_conversation("claude", f"¿Qué tablas hay en el esquema '{schema_name2}'?")
        tables2 = self.make_request("/mcp/list_tables", params={"db_name": db_name, "schema": schema_name2})
        self.print_conversation("server", f"Tablas en {schema_name2}:", tables2)
        
        # 8. Resumen final
        print("\n" + "=" * 70)
        self.print_conversation("system", "🎉 ¡Demostración completada exitosamente!")
        print("""
📋 RESUMEN DE CAPACIDADES DEMOSTRADAS:

✅ Listar bases de datos disponibles
✅ Explorar esquemas en una base de datos
✅ Listar tablas en esquemas específicos
✅ Describir estructura detallada de tablas
✅ Ejecutar consultas SQL personalizadas
✅ Manejar múltiples esquemas y tablas

🎯 TU SERVIDOR MCP ESTÁ FUNCIONANDO PERFECTAMENTE!

🔗 Para integrarlo con Claude Desktop, usa esta configuración:

{
    "mcpServers": {
        "database-explorer": {
            "command": "npx",
            "args": [
                "-y", 
                "mcp-remote@latest", 
                "http://localhost:8001/mcp",
                "--allow-http"
            ]
        }
    }
}

🌐 También puedes probarlo en: http://localhost:8001/docs
        """)

if __name__ == "__main__":
    demo = MCPDemo()
    demo.run_demo()
