from fastapi import FastAPI, Depends, HTTPException
from fastapi_mcp import FastApiMCP
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import importlib
import os
from contextlib import asynccontextmanager

# Inicializar FastAPI
app = FastAPI(
    title="Database Explorer MCP",
    description="MCP server for exploring and interacting with multiple database types",
    version="1.0.0"
)

# Inicializar MCP
mcp = FastApiMCP(app)
mcp.mount()

# Modelos de datos
class DatabaseConfig(BaseModel):
    name: str
    type: str  # "postgres", "mysql", "sqlserver"
    host: str
    port: int
    username: str
    password: str
    database: str

class TableColumn(BaseModel):
    name: str
    type: str
    nullable: bool
    primary_key: bool
    description: Optional[str] = None

class TableInfo(BaseModel):
    name: str
    schema: str
    description: Optional[str] = None
    columns: List[TableColumn]

# Registro de conectores de bases de datos
db_connectors = {}

# Función para cargar dinámicamente los conectores
def load_connectors():
    connectors_dir = os.path.join(os.path.dirname(__file__), "db", "connectors")
    for filename in os.listdir(connectors_dir):
        if filename.endswith("_connector.py") and not filename.startswith("base_"):
            module_name = filename[:-3]  # Quitar .py
            module = importlib.import_module(f"app.db.connectors.{module_name}")
            # Buscar la clase que hereda de DatabaseConnector
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and
                    hasattr(attr, '__bases__') and
                    any('DatabaseConnector' in str(base) for base in attr.__bases__)):
                    db_type = module_name.split("_")[0]  # postgres_connector.py -> postgres
                    db_connectors[db_type] = attr
                    break

# Configuración de bases de datos (en producción, cargar desde archivo o variables de entorno)
database_configs = []

# Cargar configuraciones desde variables de entorno
def load_db_configs():
    # Ejemplo: cargar una configuración de PostgreSQL desde variables de entorno
    if os.environ.get("DB_HOST"):
        postgres_config = DatabaseConfig(
            name="postgres_demo",
            type="postgres",
            host=os.environ.get("DB_HOST", "postgres"),
            port=int(os.environ.get("DB_PORT", 5432)),
            username=os.environ.get("DB_USER", "postgres"),
            password=os.environ.get("DB_PASSWORD", "postgres"),
            database=os.environ.get("DB_NAME", "postgres")
        )
        database_configs.append(postgres_config)

# Obtener conector para una base de datos específica
def get_connector(db_name: str):
    config = next((db for db in database_configs if db.name == db_name), None)
    if not config:
        raise HTTPException(status_code=404, detail=f"Database '{db_name}' not found")

    connector_class = db_connectors.get(config.type)
    if not connector_class:
        raise HTTPException(status_code=500, detail=f"No connector available for database type '{config.type}'")

    return connector_class(config)

# Endpoints MCP
@app.get("/mcp/list_databases", tags=["MCP Tools"])
async def list_databases():
    """List all available databases configured in the system"""
    return [db.name for db in database_configs]

@app.get("/mcp/list_schemas", tags=["MCP Tools"])
async def list_schemas(db_name: str):
    """List all schemas in a specific database"""
    connector = get_connector(db_name)
    return await connector.list_schemas()

@app.get("/mcp/list_tables", tags=["MCP Tools"])
async def list_tables(db_name: str, schema: str):
    """List all tables in a specific schema"""
    connector = get_connector(db_name)
    return await connector.list_tables(schema)

@app.get("/mcp/describe_table", tags=["MCP Tools"])
async def describe_table(db_name: str, schema: str, table: str):
    """Get detailed information about a specific table"""
    connector = get_connector(db_name)
    return await connector.describe_table(schema, table)

@app.post("/mcp/execute_query", tags=["MCP Tools"])
async def execute_query(db_name: str, query: str):
    """Execute a custom SQL query on a specific database"""
    connector = get_connector(db_name)
    return await connector.execute_query(query)

# Eventos de inicio y cierre
@app.on_event("startup")
async def startup():
    load_connectors()
    load_db_configs()

@app.on_event("shutdown")
async def shutdown():
    # Cerrar todas las conexiones
    for db_type, connector_class in db_connectors.items():
        await connector_class.close_all()

# Configurar servidor MCP
mcp.setup_server()
