from app.db.connectors.base_connector import DatabaseConnector
import asyncpg
from typing import List, Dict, Any

class PostgresConnector(DatabaseConnector):
    """Connector for PostgreSQL databases"""
    
    async def connect(self):
        """Establish connection to PostgreSQL database"""
        if not self.connection:
            self.connection = await asyncpg.connect(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=self.config.password,
                database=self.config.database
            )
        return self.connection
    
    async def disconnect(self):
        """Close the PostgreSQL connection"""
        if self.connection:
            await self.connection.close()
            self.connection = None
    
    async def list_schemas(self) -> List[str]:
        """List all schemas in the PostgreSQL database"""
        conn = await self.connect()
        query = """
        SELECT schema_name 
        FROM information_schema.schemata 
        WHERE schema_name NOT IN ('pg_catalog', 'information_schema')
        ORDER BY schema_name;
        """
        results = await conn.fetch(query)
        return [row['schema_name'] for row in results]
    
    async def list_tables(self, schema: str) -> List[str]:
        """List all tables in a specific schema"""
        conn = await self.connect()
        query = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = $1
        ORDER BY table_name;
        """
        results = await conn.fetch(query, schema)
        return [row['table_name'] for row in results]
    
    async def describe_table(self, schema: str, table: str) -> Dict[str, Any]:
        """Get detailed information about a specific table"""
        conn = await self.connect()
        
        # Get table description
        table_query = """
        SELECT obj_description(pgc.oid) as table_description
        FROM pg_class pgc
        JOIN pg_namespace nsp ON nsp.oid = pgc.relnamespace
        WHERE nsp.nspname = $1 AND pgc.relname = $2;
        """
        table_desc = await conn.fetchrow(table_query, schema, table)
        
        # Get columns information
        columns_query = """
        SELECT 
            c.column_name, 
            c.data_type, 
            c.is_nullable = 'YES' as is_nullable,
            CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary_key,
            col_description(
                (quote_ident($1) || '.' || quote_ident($2))::regclass::oid, 
                ordinal_position
            ) as column_description
        FROM 
            information_schema.columns c
        LEFT JOIN (
            SELECT ku.column_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage ku
                ON tc.constraint_name = ku.constraint_name
            WHERE tc.constraint_type = 'PRIMARY KEY'
                AND tc.table_schema = $1
                AND tc.table_name = $2
        ) pk ON c.column_name = pk.column_name
        WHERE 
            c.table_schema = $1
            AND c.table_name = $2
        ORDER BY 
            c.ordinal_position;
        """
        columns = await conn.fetch(columns_query, schema, table)
        
        return {
            "name": table,
            "schema": schema,
            "description": table_desc['table_description'] if table_desc else None,
            "columns": [
                {
                    "name": col['column_name'],
                    "type": col['data_type'],
                    "nullable": col['is_nullable'],
                    "primary_key": col['is_primary_key'],
                    "description": col['column_description']
                }
                for col in columns
            ]
        }
    
    async def execute_query(self, query: str) -> Dict[str, Any]:
        """Execute a custom SQL query"""
        conn = await self.connect()
        try:
            results = await conn.fetch(query)
            return {
                "success": True,
                "rows": len(results),
                "data": [dict(row) for row in results]
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    @classmethod
    async def close_all(cls):
        """Close all PostgreSQL connections"""
        # Implementar si se mantiene un pool de conexiones
        pass

# Registrar el conector
Connector = PostgresConnector