from abc import ABC, abstractmethod
from typing import List, Dict, Any

class DatabaseConnector(ABC):
    """Base abstract class for all database connectors"""
    
    def __init__(self, config):
        self.config = config
        self.connection = None
    
    @abstractmethod
    async def connect(self):
        """Establish connection to the database"""
        pass
    
    @abstractmethod
    async def disconnect(self):
        """Close the database connection"""
        pass
    
    @abstractmethod
    async def list_schemas(self) -> List[str]:
        """List all schemas in the database"""
        pass
    
    @abstractmethod
    async def list_tables(self, schema: str) -> List[str]:
        """List all tables in a specific schema"""
        pass
    
    @abstractmethod
    async def describe_table(self, schema: str, table: str) -> Dict[str, Any]:
        """Get detailed information about a specific table"""
        pass
    
    @abstractmethod
    async def execute_query(self, query: str) -> Dict[str, Any]:
        """Execute a custom SQL query"""
        pass
    
    @classmethod
    async def close_all(cls):
        """Close all connections for this connector type"""
        pass