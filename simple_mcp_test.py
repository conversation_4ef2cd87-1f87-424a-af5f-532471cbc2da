#!/usr/bin/env python3
"""
Prueba simple del servidor MCP usando requests
"""

import requests
import json

def test_mcp_endpoints():
    base_url = "http://localhost:8001"
    
    print("🧪 Prueba simple del servidor MCP")
    print("=" * 40)
    
    # Test 1: Verificar que el servidor está funcionando
    try:
        response = requests.get(f"{base_url}/docs")
        print(f"✅ Servidor funcionando - Status: {response.status_code}")
    except Exception as e:
        print(f"❌ Error conectando al servidor: {e}")
        return
    
    # Test 2: Probar endpoints MCP
    endpoints = [
        ("/mcp/list_databases", "Listar bases de datos"),
        ("/openapi.json", "Documentación OpenAPI"),
    ]
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}")
            print(f"\n📊 {description}")
            print(f"   URL: {base_url}{endpoint}")
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if endpoint == "/mcp/list_databases":
                    print(f"   Bases de datos: {data}")
                elif endpoint == "/openapi.json":
                    print(f"   Título API: {data.get('info', {}).get('title')}")
                    mcp_paths = [p for p in data.get('paths', {}).keys() if p.startswith('/mcp/')]
                    print(f"   Endpoints MCP: {len(mcp_paths)}")
            else:
                print(f"   Error: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Test 3: Probar endpoint MCP principal (SSE)
    print(f"\n🔗 Endpoint MCP SSE")
    try:
        response = requests.get(f"{base_url}/mcp", stream=True)
        print(f"   Status: {response.status_code}")
        print(f"   Content-Type: {response.headers.get('content-type')}")
        if 'text/event-stream' in response.headers.get('content-type', ''):
            print("   ✅ SSE endpoint funcionando correctamente")
        else:
            print("   ⚠️  No es un endpoint SSE válido")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 40)
    print("✅ Pruebas completadas!")
    
    print(f"""
🎯 Tu servidor MCP está funcionando en: {base_url}

📋 Formas de probarlo:

1. 🌐 Interfaz web:
   - Swagger UI: {base_url}/docs
   - ReDoc: {base_url}/redoc

2. 🔗 Endpoint MCP:
   - SSE: {base_url}/mcp

3. 📱 Integración con Claude Desktop:
   Agrega esto a tu configuración de Claude:
   
   {{
       "mcpServers": {{
           "database-explorer": {{
               "command": "npx",
               "args": [
                   "-y", 
                   "mcp-remote@latest", 
                   "{base_url}/mcp",
                   "--allow-http"
               ]
           }}
       }}
   }}

4. 🗄️ Para probar con base de datos real:
   export DB_HOST=tu_host
   export DB_PORT=5432
   export DB_USER=tu_usuario
   export DB_PASSWORD=tu_password
   export DB_NAME=tu_database
   
   Luego reinicia el servidor.
""")

if __name__ == "__main__":
    test_mcp_endpoints()
