services:
  api:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./app:/app/app
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=testuser
      - DB_PASSWORD=testpass123
      - DB_NAME=testdb
    depends_on:
      - postgres

  postgres:
    image: postgres:14
    container_name: mcp_postgres
    environment:
      - POSTGRES_USER=testuser
      - POSTGRES_PASSWORD=testpass123
      - POSTGRES_DB=testdb
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U testuser -d testdb"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
