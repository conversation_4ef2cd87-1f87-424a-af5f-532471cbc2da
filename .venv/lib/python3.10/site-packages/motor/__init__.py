# Copyright 2011-present MongoDB, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Motor, an asynchronous driver for MongoDB."""
from ._version import get_version_string, version, version_tuple  # noqa: F401

"""Current version of Motor."""


try:
    import tornado
except ImportError:
    tornado = None  # type:ignore[assignment]
else:
    # For backwards compatibility with Motor 0.4, export Motor's Tornado classes
    # at module root. This may change in the future.
    from .motor_tornado import *  # noqa: F403
    from .motor_tornado import __all__  # noqa: F401
